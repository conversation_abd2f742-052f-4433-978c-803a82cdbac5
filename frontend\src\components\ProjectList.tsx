import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Table, 
  Space, 
  message, 
  Typography,
  Empty,
  Spin
} from 'antd';
import { 
  PlusOutlined, 
  ExperimentOutlined, 
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { projectApi } from '../services/api';
import type { Project } from '../types';
import TestConnection from './TestConnection';

const { Title, Text } = Typography;
const { TextArea } = Input;

const ProjectList: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    setLoading(true);
    try {
      const data = await projectApi.getProjects();
      setProjects(data);
    } catch (error) {
      message.error('加载项目列表失败');
      console.error('Load projects error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async (values: { name: string; description?: string }) => {
    console.log('Creating project with values:', values);
    try {
      const newProject = await projectApi.createProject(values);
      console.log('Project created successfully:', newProject);
      message.success('项目创建成功');
      setModalVisible(false);
      form.resetFields();
      setProjects([newProject, ...projects]);
    } catch (error: any) {
      console.error('Create project error:', error);
      message.error(`创建项目失败: ${error.response?.data?.error || error.message || '未知错误'}`);
    }
  };

  const handleProjectClick = (projectId: string) => {
    navigate(`/project/${projectId}`);
  };

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Project) => (
        <Button 
          type="link" 
          onClick={() => handleProjectClick(record.id)}
          style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}
        >
          <ExperimentOutlined style={{ marginRight: 8 }} />
          {text}
        </Button>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || <Text type="secondary">无描述</Text>,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => (
        <Space>
          <CalendarOutlined />
          {new Date(text).toLocaleString('zh-CN')}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Project) => (
        <Button 
          type="primary" 
          size="small"
          onClick={() => handleProjectClick(record.id)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <div>
      <TestConnection />
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24,
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <Title level={2} style={{ margin: 0 }}>
          <ExperimentOutlined style={{ marginRight: 12 }} />
          引物设计项目
        </Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setModalVisible(true)}
          size="large"
        >
          新建项目
        </Button>
      </div>

      <Card>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
          </div>
        ) : projects.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无项目，点击上方按钮创建第一个项目"
          />
        ) : (
          <Table
            columns={columns}
            dataSource={projects}
            rowKey="id"
            scroll={{ x: 800 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个项目`,
              responsive: true
            }}
          />
        )}
      </Card>

      <Modal
        title="创建新项目"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="创建"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateProject}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[
              { required: true, message: '请输入项目名称' },
              { max: 100, message: '项目名称不能超过100个字符' }
            ]}
          >
            <Input 
              placeholder="请输入项目名称"
              prefix={<FileTextOutlined />}
            />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
            rules={[
              { max: 500, message: '项目描述不能超过500个字符' }
            ]}
          >
            <TextArea 
              rows={4}
              placeholder="请输入项目描述（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectList;
