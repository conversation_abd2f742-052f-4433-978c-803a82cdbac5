<!DOCTYPE html>
<html>
<head>
    <title>API连接测试</title>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testHealth()">测试健康检查</button>
    <button onclick="testCreateProject()">测试创建项目</button>
    <div id="result"></div>

    <script>
        async function testHealth() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>错误: ' + error.message + '</pre>';
            }
        }

        async function testCreateProject() {
            try {
                const response = await fetch('http://localhost:5000/api/projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: '测试项目',
                        description: '这是一个测试项目'
                    })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>错误: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
