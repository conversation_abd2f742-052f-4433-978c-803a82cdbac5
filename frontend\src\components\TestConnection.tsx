import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, message } from 'antd';
import { projectApi } from '../services/api';

const { Title, Text } = Typography;

const TestConnection: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testHealth = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      setResult(data);
      message.success('健康检查成功');
    } catch (error: any) {
      console.error('Health check error:', error);
      message.error('健康检查失败: ' + error.message);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testCreateProject = async () => {
    setLoading(true);
    try {
      const newProject = await projectApi.createProject({
        name: '测试项目 ' + new Date().toLocaleTimeString(),
        description: '这是一个API连接测试项目'
      });
      setResult(newProject);
      message.success('项目创建成功');
    } catch (error: any) {
      console.error('Create project error:', error);
      message.error('创建项目失败: ' + (error.response?.data?.error || error.message));
      setResult({ error: error.response?.data?.error || error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="API连接测试" style={{ margin: '20px 0' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space>
          <Button onClick={testHealth} loading={loading}>
            测试健康检查
          </Button>
          <Button onClick={testCreateProject} loading={loading} type="primary">
            测试创建项目
          </Button>
        </Space>
        
        {result && (
          <Card size="small" title="测试结果">
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto'
            }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </Card>
        )}
      </Space>
    </Card>
  );
};

export default TestConnection;
