const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const { exec } = require('child_process');
const sqlite3 = require('sqlite3').verbose();

const app = express();
const PORT = process.env.PORT || 5000;

// 中间件配置
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const resultsDir = path.join(__dirname, 'results');
const dbDir = path.join(__dirname, 'database');

fs.ensureDirSync(uploadsDir);
fs.ensureDirSync(resultsDir);
fs.ensureDirSync(dbDir);

// 数据库初始化
const db = new sqlite3.Database(path.join(dbDir, 'primer3.db'));

// 创建数据表
db.serialize(() => {
  // 项目表
  db.run(`CREATE TABLE IF NOT EXISTS projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // 序列表
  db.run(`CREATE TABLE IF NOT EXISTS sequences (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    name TEXT NOT NULL,
    sequence TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id)
  )`);

  // 引物设计任务表
  db.run(`CREATE TABLE IF NOT EXISTS primer_jobs (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    sequence_id TEXT,
    parameters TEXT,
    status TEXT DEFAULT 'pending',
    result TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (project_id) REFERENCES projects (id),
    FOREIGN KEY (sequence_id) REFERENCES sequences (id)
  )`);
});

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = ['.fasta', '.fa', '.txt', '.seq'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 FASTA (.fasta, .fa), TXT (.txt), SEQ (.seq) 格式的文件'));
    }
  }
});

// API路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Primer3 Web API is running' });
});

// 获取所有项目
app.get('/api/projects', (req, res) => {
  db.all('SELECT * FROM projects ORDER BY created_at DESC', (err, rows) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(rows);
  });
});

// 创建新项目
app.post('/api/projects', (req, res) => {
  const { name, description } = req.body;
  const id = uuidv4();
  
  db.run('INSERT INTO projects (id, name, description) VALUES (?, ?, ?)', 
    [id, name, description], function(err) {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ id, name, description, created_at: new Date().toISOString() });
  });
});

// 获取项目详情
app.get('/api/projects/:id', (req, res) => {
  const projectId = req.params.id;
  
  db.get('SELECT * FROM projects WHERE id = ?', [projectId], (err, project) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }
    
    // 获取项目的序列和任务
    db.all('SELECT * FROM sequences WHERE project_id = ?', [projectId], (err, sequences) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      
      db.all('SELECT * FROM primer_jobs WHERE project_id = ?', [projectId], (err, jobs) => {
        if (err) {
          return res.status(500).json({ error: err.message });
        }
        
        res.json({
          ...project,
          sequences,
          jobs
        });
      });
    });
  });
});

// 上传序列文件
app.post('/api/projects/:id/upload', upload.single('file'), (req, res) => {
  const projectId = req.params.id;
  const file = req.file;

  if (!file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  // 解析FASTA文件
  const filePath = file.path;
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to read file' });
    }

    const sequences = parseFastaFile(data);
    const sequenceIds = [];

    // 批量插入序列到数据库
    const insertPromises = sequences.map(seq => {
      return new Promise((resolve, reject) => {
        const seqId = uuidv4();
        db.run('INSERT INTO sequences (id, project_id, name, sequence) VALUES (?, ?, ?, ?)',
          [seqId, projectId, seq.name, seq.sequence], (err) => {
          if (err) reject(err);
          else {
            sequenceIds.push(seqId);
            resolve(seqId);
          }
        });
      });
    });

    Promise.all(insertPromises)
      .then(() => {
        // 删除临时文件
        fs.unlink(filePath, () => {});
        res.json({
          message: `Successfully uploaded ${sequences.length} sequences`,
          sequenceIds,
          sequences: sequences.map((seq, index) => ({
            id: sequenceIds[index],
            name: seq.name,
            length: seq.sequence.length
          }))
        });
      })
      .catch(err => {
        res.status(500).json({ error: err.message });
      });
  });
});

// 手动添加序列
app.post('/api/projects/:id/sequences', (req, res) => {
  const projectId = req.params.id;
  const { name, sequence } = req.body;

  if (!name || !sequence) {
    return res.status(400).json({ error: 'Name and sequence are required' });
  }

  const seqId = uuidv4();
  db.run('INSERT INTO sequences (id, project_id, name, sequence) VALUES (?, ?, ?, ?)',
    [seqId, projectId, name, sequence], (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ id: seqId, name, sequence: sequence.substring(0, 100) + '...' });
  });
});

// 创建引物设计任务
app.post('/api/projects/:id/design', (req, res) => {
  const projectId = req.params.id;
  const { sequenceIds, parameters } = req.body;

  if (!sequenceIds || sequenceIds.length === 0) {
    return res.status(400).json({ error: 'No sequences selected' });
  }

  const jobIds = [];
  const insertPromises = sequenceIds.map(seqId => {
    return new Promise((resolve, reject) => {
      const jobId = uuidv4();
      db.run('INSERT INTO primer_jobs (id, project_id, sequence_id, parameters, status) VALUES (?, ?, ?, ?, ?)',
        [jobId, projectId, seqId, JSON.stringify(parameters), 'pending'], (err) => {
        if (err) reject(err);
        else {
          jobIds.push(jobId);
          resolve(jobId);
        }
      });
    });
  });

  Promise.all(insertPromises)
    .then(() => {
      // 异步处理引物设计任务
      processDesignJobs(jobIds);
      res.json({
        message: `Created ${jobIds.length} design jobs`,
        jobIds
      });
    })
    .catch(err => {
      res.status(500).json({ error: err.message });
    });
});

// 获取任务状态
app.get('/api/jobs/:id', (req, res) => {
  const jobId = req.params.id;

  db.get('SELECT * FROM primer_jobs WHERE id = ?', [jobId], (err, job) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.json({
      ...job,
      parameters: JSON.parse(job.parameters),
      result: job.result ? JSON.parse(job.result) : null
    });
  });
});

// 导出结果
app.get('/api/projects/:id/export', (req, res) => {
  const projectId = req.params.id;

  db.all(`
    SELECT pj.*, s.name as sequence_name, s.sequence
    FROM primer_jobs pj
    JOIN sequences s ON pj.sequence_id = s.id
    WHERE pj.project_id = ? AND pj.status = 'completed'
  `, [projectId], (err, jobs) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }

    const exportData = jobs.map(job => ({
      sequence_name: job.sequence_name,
      sequence: job.sequence,
      parameters: JSON.parse(job.parameters),
      result: JSON.parse(job.result),
      created_at: job.created_at,
      completed_at: job.completed_at
    }));

    res.json(exportData);
  });
});

// 辅助函数

// 解析FASTA文件
function parseFastaFile(data) {
  const sequences = [];
  const lines = data.split('\n').map(line => line.trim()).filter(line => line);

  let currentSeq = null;

  for (const line of lines) {
    if (line.startsWith('>')) {
      if (currentSeq) {
        sequences.push(currentSeq);
      }
      currentSeq = {
        name: line.substring(1).trim(),
        sequence: ''
      };
    } else if (currentSeq) {
      currentSeq.sequence += line.toUpperCase();
    }
  }

  if (currentSeq) {
    sequences.push(currentSeq);
  }

  return sequences;
}

// 异步处理引物设计任务
async function processDesignJobs(jobIds) {
  for (const jobId of jobIds) {
    try {
      await processDesignJob(jobId);
    } catch (error) {
      console.error(`Error processing job ${jobId}:`, error);
      // 更新任务状态为失败
      db.run('UPDATE primer_jobs SET status = ?, result = ? WHERE id = ?',
        ['failed', JSON.stringify({ error: error.message }), jobId]);
    }
  }
}

// 处理单个引物设计任务
function processDesignJob(jobId) {
  return new Promise((resolve, reject) => {
    // 获取任务信息
    db.get(`
      SELECT pj.*, s.sequence, s.name as sequence_name
      FROM primer_jobs pj
      JOIN sequences s ON pj.sequence_id = s.id
      WHERE pj.id = ?
    `, [jobId], (err, job) => {
      if (err) {
        return reject(err);
      }

      // 更新状态为处理中
      db.run('UPDATE primer_jobs SET status = ? WHERE id = ?', ['running', jobId]);

      const parameters = JSON.parse(job.parameters);
      const inputFile = path.join(resultsDir, `${jobId}_input.txt`);
      const outputFile = path.join(resultsDir, `${jobId}_output.txt`);

      // 创建primer3输入文件
      const primer3Input = createPrimer3Input(job.sequence, parameters);

      fs.writeFile(inputFile, primer3Input, (err) => {
        if (err) {
          return reject(err);
        }

        // 调用primer3容器 (Windows路径格式修复)
        const resultsPath = resultsDir.replace(/\\/g, '/');
        const dockerCmd = `docker run --rm -v "${resultsPath}:/data" biocontainers/primer3:v2.4.0-2-deb_cv1 sh -c "primer3_core < /data/${jobId}_input.txt > /data/${jobId}_output.txt"`;

        exec(dockerCmd, (error, stdout, stderr) => {
          if (error) {
            console.error('Primer3 execution error:', error);
            return reject(error);
          }

          // 读取结果文件
          fs.readFile(outputFile, 'utf8', (err, data) => {
            if (err) {
              return reject(err);
            }

            try {
              const result = parsePrimer3Output(data);

              // 保存结果到数据库
              db.run('UPDATE primer_jobs SET status = ?, result = ?, completed_at = ? WHERE id = ?',
                ['completed', JSON.stringify(result), new Date().toISOString(), jobId], (err) => {
                if (err) {
                  return reject(err);
                }

                // 清理临时文件
                fs.unlink(inputFile, () => {});
                fs.unlink(outputFile, () => {});

                resolve(result);
              });
            } catch (parseError) {
              reject(parseError);
            }
          });
        });
      });
    });
  });
}

// 创建primer3输入文件内容
function createPrimer3Input(sequence, parameters) {
  const defaults = {
    PRIMER_OPT_SIZE: 20,
    PRIMER_MIN_SIZE: 18,
    PRIMER_MAX_SIZE: 25,
    PRIMER_OPT_TM: 60.0,
    PRIMER_MIN_TM: 57.0,
    PRIMER_MAX_TM: 63.0,
    PRIMER_MIN_GC: 20.0,
    PRIMER_MAX_GC: 80.0,
    PRIMER_MAX_POLY_X: 5,
    PRIMER_PRODUCT_SIZE_RANGE: '75-100 100-300 300-500 500-700 700-1000',
    PRIMER_NUM_RETURN: 5
  };

  const config = { ...defaults, ...parameters };

  let input = `SEQUENCE_ID=sequence\n`;
  input += `SEQUENCE_TEMPLATE=${sequence}\n`;

  Object.entries(config).forEach(([key, value]) => {
    input += `${key}=${value}\n`;
  });

  input += `=\n`;

  return input;
}

// 解析primer3输出结果
function parsePrimer3Output(data) {
  const lines = data.split('\n');
  const result = {
    primers: [],
    statistics: {}
  };

  for (const line of lines) {
    if (line.includes('=')) {
      const [key, value] = line.split('=');

      if (key.startsWith('PRIMER_LEFT_') || key.startsWith('PRIMER_RIGHT_') || key.startsWith('PRIMER_INTERNAL_')) {
        const match = key.match(/PRIMER_(LEFT|RIGHT|INTERNAL)_(\d+)(_SEQUENCE)?$/);
        if (match) {
          const type = match[1].toLowerCase();
          const index = parseInt(match[2]);
          const isSequence = match[3] === '_SEQUENCE';

          if (!result.primers[index]) {
            result.primers[index] = { index };
          }

          if (!result.primers[index][type]) {
            result.primers[index][type] = {};
          }

          if (isSequence) {
            result.primers[index][type].sequence = value;
          } else {
            // 解析位置信息 "start,length"
            const [start, length] = value.split(',');
            result.primers[index][type].start = start;
            result.primers[index][type].len = length;
          }
        } else {
          // 处理其他属性如TM, GC等
          const propMatch = key.match(/PRIMER_(LEFT|RIGHT|INTERNAL)_(\d+)_(.+)/);
          if (propMatch) {
            const type = propMatch[1].toLowerCase();
            const index = parseInt(propMatch[2]);
            const property = propMatch[3].toLowerCase();

            if (!result.primers[index]) {
              result.primers[index] = { index };
            }

            if (!result.primers[index][type]) {
              result.primers[index][type] = {};
            }

            result.primers[index][type][property] = value;
          }
        }
      } else if (key.startsWith('PRIMER_PRODUCT_SIZE_')) {
        const index = parseInt(key.split('_')[3]);
        if (!result.primers[index]) {
          result.primers[index] = { index };
        }
        result.primers[index].product_size = value;
      } else {
        result.statistics[key] = value;
      }
    }
  }

  // 过滤掉空的引物对象
  result.primers = result.primers.filter(p => p && (p.left || p.right));

  return result;
}

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
