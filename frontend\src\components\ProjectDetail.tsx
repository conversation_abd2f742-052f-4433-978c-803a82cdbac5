import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Tabs,
  Button,
  Space,
  message,
  Typography,
  Spin,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  UploadOutlined,
  SettingOutlined,
  ExperimentOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { projectApi } from '../services/api';
import type { Project } from '../types';
import SequenceUpload from './SequenceUpload';
import ParameterSettings from './ParameterSettings';
import ResultsView from './ResultsView';

const { Title, Text } = Typography;


const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');

  useEffect(() => {
    if (id) {
      loadProject();
    }
  }, [id]);

  const loadProject = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const data = await projectApi.getProject(id);
      setProject(data);
    } catch (error) {
      message.error('加载项目详情失败');
      console.error('Load project error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportResults = async () => {
    if (!id) return;
    
    try {
      const data = await projectApi.exportResults(id);
      
      // 创建下载链接
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `primer3_results_${project?.name || 'project'}_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      message.success('结果导出成功');
    } catch (error) {
      message.error('导出结果失败');
      console.error('Export results error:', error);
    }
  };

  const getTabItems = () => {
    const items = [
      {
        key: 'upload',
        label: (
          <span>
            <UploadOutlined />
            序列上传
          </span>
        ),
        children: project ? (
          <SequenceUpload 
            project={project} 
            onSequenceAdded={loadProject}
            onNext={() => setActiveTab('settings')}
          />
        ) : null,
      },
      {
        key: 'settings',
        label: (
          <span>
            <SettingOutlined />
            参数设置
          </span>
        ),
        children: project ? (
          <ParameterSettings 
            project={project}
            onDesignStarted={() => setActiveTab('results')}
          />
        ) : null,
      },
      {
        key: 'results',
        label: (
          <span>
            <ExperimentOutlined />
            设计结果
          </span>
        ),
        children: project ? (
          <ResultsView 
            project={project}
            onRefresh={loadProject}
          />
        ) : null,
      },
    ];

    return items;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载项目详情...</Text>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <Alert
        message="项目不存在"
        description="请检查项目ID是否正确，或返回项目列表重新选择。"
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/')}>
            返回项目列表
          </Button>
        }
      />
    );
  }

  const completedJobs = project.jobs?.filter(job => job.status === 'completed') || [];
  const hasResults = completedJobs.length > 0;

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24,
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
          >
            返回列表
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {project.name}
          </Title>
        </Space>
        
        <Space>
          {hasResults && (
            <Button 
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleExportResults}
            >
              导出结果
            </Button>
          )}
        </Space>
      </div>

      {project.description && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Text type="secondary">{project.description}</Text>
        </Card>
      )}

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={getTabItems()}
          size="large"
        />
      </Card>
    </div>
  );
};

export default ProjectDetail;
