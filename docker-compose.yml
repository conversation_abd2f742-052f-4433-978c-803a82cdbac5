version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - primer3-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./backend/database:/app/database
      - ./backend/uploads:/app/uploads
      - ./backend/results:/app/results
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - NODE_ENV=production
      - PORT=5000
    networks:
      - primer3-network
    restart: unless-stopped

  # Primer3容器（预拉取镜像）
  primer3:
    image: biocontainers/primer3:v2.4.0-2-deb_cv1
    command: ["sleep", "infinity"]
    networks:
      - primer3-network
    restart: unless-stopped

networks:
  primer3-network:
    driver: bridge

volumes:
  database_data:
  uploads_data:
  results_data:
