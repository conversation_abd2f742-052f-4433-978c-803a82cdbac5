import React, { useState } from 'react';
import {
  Card,
  Form,
  InputN<PERSON>ber,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Alert,
  Checkbox,
  message,
  Input
} from 'antd';
import {
  SettingOutlined,
  PlayCircleOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import { projectApi, Project, PrimerParameters } from '../services/api';

const { Title, Text } = Typography;

interface ParameterSettingsProps {
  project: Project;
  onDesignStarted: () => void;
}

const ParameterSettings: React.FC<ParameterSettingsProps> = ({ 
  project, 
  onDesignStarted 
}) => {
  const [form] = Form.useForm();
  const [selectedSequences, setSelectedSequences] = useState<string[]>([]);
  const [designing, setDesigning] = useState(false);

  const sequences = project.sequences || [];
  const hasSequences = sequences.length > 0;

  const defaultParameters: PrimerParameters = {
    PRIMER_OPT_SIZE: 20,
    PRIMER_MIN_SIZE: 18,
    PRIMER_MAX_SIZE: 25,
    PRIMER_OPT_TM: 60.0,
    PRIMER_MIN_TM: 57.0,
    PRIMER_MAX_TM: 63.0,
    PRIMER_MIN_GC: 20.0,
    PRIMER_MAX_GC: 80.0,
    PRIMER_MAX_POLY_X: 5,
    PRIMER_PRODUCT_SIZE_RANGE: '75-100 100-300 300-500 500-700 700-1000',
    PRIMER_NUM_RETURN: 5
  };

  const handleStartDesign = async (values: PrimerParameters) => {
    if (selectedSequences.length === 0) {
      message.error('请至少选择一条序列进行引物设计');
      return;
    }

    setDesigning(true);
    try {
      await projectApi.createDesignJob(project.id, {
        sequenceIds: selectedSequences,
        parameters: values
      });
      message.success(`已为 ${selectedSequences.length} 条序列创建引物设计任务`);
      onDesignStarted();
    } catch (error) {
      message.error('创建引物设计任务失败');
      console.error('Design job error:', error);
    } finally {
      setDesigning(false);
    }
  };

  const handleSequenceSelection = (sequenceId: string, checked: boolean) => {
    if (checked) {
      setSelectedSequences([...selectedSequences, sequenceId]);
    } else {
      setSelectedSequences(selectedSequences.filter(id => id !== sequenceId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSequences(sequences.map(seq => seq.id));
    } else {
      setSelectedSequences([]);
    }
  };

  if (!hasSequences) {
    return (
      <Alert
        message="请先上传序列"
        description="在进行引物设计之前，请先在"序列上传"标签页中添加目标序列。"
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div>
      <Title level={4}>引物设计参数</Title>
      <Text type="secondary">
        配置引物设计参数，选择目标序列，然后开始批量引物设计。
      </Text>

      <Divider />

      {/* 序列选择 */}
      <Card title="选择目标序列" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <Checkbox
            checked={selectedSequences.length === sequences.length}
            indeterminate={selectedSequences.length > 0 && selectedSequences.length < sequences.length}
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            全选 ({selectedSequences.length}/{sequences.length})
          </Checkbox>
        </div>
        
        <Row gutter={[16, 16]}>
          {sequences.map(sequence => (
            <Col span={12} key={sequence.id}>
              <Card size="small">
                <Checkbox
                  checked={selectedSequences.includes(sequence.id)}
                  onChange={(e) => handleSequenceSelection(sequence.id, e.target.checked)}
                >
                  <Space direction="vertical" size="small">
                    <Text strong>{sequence.name}</Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      长度: {sequence.sequence.length} bp
                    </Text>
                    <Text code style={{ fontSize: '11px' }}>
                      {sequence.sequence.substring(0, 40)}...
                    </Text>
                  </Space>
                </Checkbox>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 参数设置 */}
      <Card title="引物参数设置">
        <Form
          form={form}
          layout="vertical"
          initialValues={defaultParameters}
          onFinish={handleStartDesign}
        >
          <Row gutter={24}>
            <Col span={8}>
              <Title level={5}>引物长度设置</Title>
              <Form.Item
                name="PRIMER_OPT_SIZE"
                label="最适长度 (bp)"
                rules={[{ required: true, message: '请输入最适长度' }]}
              >
                <InputNumber min={15} max={35} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MIN_SIZE"
                label="最小长度 (bp)"
                rules={[{ required: true, message: '请输入最小长度' }]}
              >
                <InputNumber min={15} max={30} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MAX_SIZE"
                label="最大长度 (bp)"
                rules={[{ required: true, message: '请输入最大长度' }]}
              >
                <InputNumber min={20} max={40} style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Title level={5}>熔解温度设置</Title>
              <Form.Item
                name="PRIMER_OPT_TM"
                label="最适Tm (°C)"
                rules={[{ required: true, message: '请输入最适Tm' }]}
              >
                <InputNumber min={50} max={70} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MIN_TM"
                label="最小Tm (°C)"
                rules={[{ required: true, message: '请输入最小Tm' }]}
              >
                <InputNumber min={45} max={65} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MAX_TM"
                label="最大Tm (°C)"
                rules={[{ required: true, message: '请输入最大Tm' }]}
              >
                <InputNumber min={55} max={75} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Title level={5}>其他参数</Title>
              <Form.Item
                name="PRIMER_MIN_GC"
                label="最小GC含量 (%)"
                rules={[{ required: true, message: '请输入最小GC含量' }]}
              >
                <InputNumber min={0} max={100} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MAX_GC"
                label="最大GC含量 (%)"
                rules={[{ required: true, message: '请输入最大GC含量' }]}
              >
                <InputNumber min={0} max={100} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="PRIMER_MAX_POLY_X"
                label="最大连续碱基数"
                rules={[{ required: true, message: '请输入最大连续碱基数' }]}
              >
                <InputNumber min={3} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="PRIMER_PRODUCT_SIZE_RANGE"
                label="产物大小范围 (bp)"
                rules={[{ required: true, message: '请输入产物大小范围' }]}
              >
                <Input placeholder="例如: 75-100 100-300 300-500" />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="PRIMER_NUM_RETURN"
                label="返回引物对数量"
                rules={[{ required: true, message: '请输入返回引物对数量' }]}
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Space size="large">
              <Text type="secondary">
                已选择 {selectedSequences.length} 条序列进行引物设计
              </Text>
              <Button
                type="primary"
                size="large"
                icon={<PlayCircleOutlined />}
                loading={designing}
                htmlType="submit"
                disabled={selectedSequences.length === 0}
              >
                开始引物设计
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default ParameterSettings;
