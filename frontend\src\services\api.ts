import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 类型定义
export interface Project {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  sequences?: Sequence[];
  jobs?: PrimerJob[];
}

export interface Sequence {
  id: string;
  project_id: string;
  name: string;
  sequence: string;
  created_at: string;
}

export interface PrimerJob {
  id: string;
  project_id: string;
  sequence_id: string;
  parameters: PrimerParameters;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: PrimerResult;
  created_at: string;
  completed_at?: string;
}

export interface PrimerParameters {
  PRIMER_OPT_SIZE?: number;
  PRIMER_MIN_SIZE?: number;
  PRIMER_MAX_SIZE?: number;
  PRIMER_OPT_TM?: number;
  PRIMER_MIN_TM?: number;
  PRIMER_MAX_TM?: number;
  PRIMER_MIN_GC?: number;
  PRIMER_MAX_GC?: number;
  PRIMER_MAX_POLY_X?: number;
  PRIMER_PRODUCT_SIZE_RANGE?: string;
  PRIMER_NUM_RETURN?: number;
}

export interface PrimerResult {
  primers: Array<{
    index: number;
    left?: {
      sequence?: string;
      start?: string;
      len?: string;
      tm?: string;
      gc_percent?: string;
    };
    right?: {
      sequence?: string;
      start?: string;
      len?: string;
      tm?: string;
      gc_percent?: string;
    };
    product_size?: string;
  }>;
  statistics: Record<string, string>;
}

// API方法
export const projectApi = {
  // 获取所有项目
  getProjects: (): Promise<Project[]> => api.get('/projects'),
  
  // 创建项目
  createProject: (data: { name: string; description?: string }): Promise<Project> =>
    api.post('/projects', data),
  
  // 获取项目详情
  getProject: (id: string): Promise<Project> => api.get(`/projects/${id}`),
  
  // 上传序列文件
  uploadSequences: (projectId: string, file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(`/projects/${projectId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 手动添加序列
  addSequence: (projectId: string, data: { name: string; sequence: string }): Promise<Sequence> =>
    api.post(`/projects/${projectId}/sequences`, data),
  
  // 创建引物设计任务
  createDesignJob: (projectId: string, data: { sequenceIds: string[]; parameters: PrimerParameters }): Promise<any> =>
    api.post(`/projects/${projectId}/design`, data),
  
  // 获取任务状态
  getJob: (jobId: string): Promise<PrimerJob> => api.get(`/jobs/${jobId}`),
  
  // 导出结果
  exportResults: (projectId: string): Promise<any> => api.get(`/projects/${projectId}/export`),
};

export default api;
