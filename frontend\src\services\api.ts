import axios from 'axios';
import type { Project, Sequence, PrimerJob, PrimerParameters, PrimerResult } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// API方法定义

// API方法
export const projectApi = {
  // 获取所有项目
  getProjects: (): Promise<Project[]> => api.get('/projects'),
  
  // 创建项目
  createProject: (data: { name: string; description?: string }): Promise<Project> =>
    api.post('/projects', data),
  
  // 获取项目详情
  getProject: (id: string): Promise<Project> => api.get(`/projects/${id}`),
  
  // 上传序列文件
  uploadSequences: (projectId: string, file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(`/projects/${projectId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 手动添加序列
  addSequence: (projectId: string, data: { name: string; sequence: string }): Promise<Sequence> =>
    api.post(`/projects/${projectId}/sequences`, data),
  
  // 创建引物设计任务
  createDesignJob: (projectId: string, data: { sequenceIds: string[]; parameters: PrimerParameters }): Promise<any> =>
    api.post(`/projects/${projectId}/design`, data),
  
  // 获取任务状态
  getJob: (jobId: string): Promise<PrimerJob> => api.get(`/jobs/${jobId}`),
  
  // 导出结果
  exportResults: (projectId: string): Promise<any> => api.get(`/projects/${projectId}/export`),
};

// 导出API实例
export { api };
