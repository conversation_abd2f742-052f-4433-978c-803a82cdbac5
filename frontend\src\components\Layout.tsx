import React from 'react';
import { Layout as AntLayout, Menu, Typography, Breadcrumb } from 'antd';
import { HomeOutlined, ExperimentOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Header, Content, Footer } = AntLayout;
const { Title } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '项目列表',
    },
  ];

  const handleMenuClick = (e: any) => {
    navigate(e.key);
  };

  const getBreadcrumbItems = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const items = [
      {
        title: (
          <span>
            <HomeOutlined />
            <span style={{ marginLeft: 8 }}>首页</span>
          </span>
        ),
      },
    ];

    if (pathSegments.length > 0) {
      if (pathSegments[0] === 'project') {
        items.push({
          title: (
            <span>
              <ExperimentOutlined />
              <span style={{ marginLeft: 8 }}>项目详情</span>
            </span>
          ),
        });
      }
    }

    return items;
  };

  return (
    <AntLayout style={{
      minHeight: '100vh',
      width: '100%',
      margin: 0,
      padding: 0
    }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        background: '#001529',
        padding: '0 24px',
        width: '100%',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div style={{
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold',
          marginRight: '40px',
          whiteSpace: 'nowrap'
        }}>
          <ExperimentOutlined style={{ marginRight: 8 }} />
          Primer3 引物设计工具
        </div>
        <Menu
          theme="dark"
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            flex: 1,
            minWidth: 0,
            border: 'none'
          }}
        />
      </Header>

      <Content style={{
        padding: '24px',
        width: '100%',
        maxWidth: '1400px',
        margin: '0 auto',
        flex: 1
      }}>
        <Breadcrumb
          items={getBreadcrumbItems()}
          style={{ marginBottom: 16 }}
        />

        <div style={{
          background: '#fff',
          padding: '24px',
          minHeight: 'calc(100vh - 200px)',
          borderRadius: 8,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          width: '100%'
        }}>
          {children}
        </div>
      </Content>

      <Footer style={{
        textAlign: 'center',
        background: '#f0f2f5',
        width: '100%',
        marginTop: 'auto'
      }}>
        Primer3 Web Interface ©2024 Created for Bioinformatics Research
      </Footer>
    </AntLayout>
  );
};

export default Layout;
