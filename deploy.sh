#!/bin/bash

# Primer3 Web Interface 部署脚本

echo "🚀 开始部署 Primer3 Web Interface..."

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 预拉取Primer3镜像
echo "📥 拉取 Primer3 镜像..."
docker pull biocontainers/primer3:v2.4.0-2-deb_cv1

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查后端健康状态
echo "🏥 检查后端健康状态..."
for i in {1..30}; do
    if curl -f http://localhost:5000/api/health &> /dev/null; then
        echo "✅ 后端服务启动成功"
        break
    fi
    echo "等待后端服务启动... ($i/30)"
    sleep 2
done

# 显示访问信息
echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息："
echo "  - 前端界面: http://localhost:3000"
echo "  - 后端API:  http://localhost:5000/api"
echo ""
echo "📁 数据目录："
echo "  - 数据库: ./backend/database"
echo "  - 上传文件: ./backend/uploads"
echo "  - 结果文件: ./backend/results"
echo ""
echo "🔧 管理命令："
echo "  - 查看日志: docker-compose logs -f"
echo "  - 停止服务: docker-compose down"
echo "  - 重启服务: docker-compose restart"
echo ""
echo "💡 提示：请确保防火墙允许3000端口访问"
