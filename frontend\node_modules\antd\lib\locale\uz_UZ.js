"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _uz_UZ = _interopRequireDefault(require("rc-pagination/lib/locale/uz_UZ"));
var _uz_UZ2 = _interopRequireDefault(require("../calendar/locale/uz_UZ"));
var _uz_UZ3 = _interopRequireDefault(require("../date-picker/locale/uz_UZ"));
var _uz_UZ4 = _interopRequireDefault(require("../time-picker/locale/uz_UZ"));
const typeTemplate = '${label} ${type} turi emas';
const localeValues = {
  // NOTE: In
  // https://github.com/react-component/picker/blob/master/src/locale/uz_UZ.ts
  // and
  // https://github.com/react-component/pagination/blob/master/src/locale/uz_UZ.ts
  // both implemented as uz-latn Uzbek
  locale: 'uz-latn',
  Pagination: _uz_UZ.default,
  DatePicker: _uz_UZ3.default,
  TimePicker: _uz_UZ4.default,
  Calendar: _uz_UZ2.default,
  global: {
    placeholder: 'Iltimos tanlang',
    close: 'Yopish'
  },
  Table: {
    filterTitle: 'Filtr',
    filterConfirm: 'OK',
    filterReset: 'Bekor qilish',
    filterEmptyText: 'Filtrlarsiz',
    filterCheckAll: 'Barcha elementlarni tanlash',
    filterSearchPlaceholder: 'Filtrlarda qidiruv',
    emptyText: "Ma'lumotlar topilmadi",
    selectAll: 'Barchasini tanlash',
    selectInvert: 'Tanlovni aylantirish',
    selectNone: "Barcha ma'lumotlarni tozalang",
    selectionAll: 'Barchasini tanlash',
    sortTitle: 'Tartiblash',
    expand: 'Satirni yozish',
    collapse: "Satirni yig'ish",
    triggerDesc: 'Kamayish tartibida tartiblash uchun bosing',
    triggerAsc: "O'sish tartibida tartiblash uchun bosing",
    cancelSort: 'Tartiblshni rad etish uchun bosing'
  },
  Tour: {
    Next: "So'ngra",
    Previous: 'Ortga',
    Finish: 'Tugatish'
  },
  Modal: {
    okText: 'OK',
    cancelText: 'Yopish',
    justOkText: 'OK'
  },
  Popconfirm: {
    okText: 'OK',
    cancelText: 'Bekor qilish'
  },
  Transfer: {
    titles: ['', ''],
    searchPlaceholder: 'Qidiruv',
    itemUnit: 'elem.',
    itemsUnit: 'elem.',
    remove: 'Oʻchirish',
    selectAll: "Barch ma'lumotlarni tanlash",
    selectCurrent: 'Joriy sahifani tanlash',
    selectInvert: 'Tanlovni aylantirish',
    removeAll: "Barcha ma'lumotlarni o'chirish",
    removeCurrent: "Joriy sahifani o'chirish"
  },
  Upload: {
    uploading: 'Yuklanmoqda...',
    removeFile: "Faylni o'chirish",
    uploadError: 'Yuklashda xatolik yuz berdi',
    previewFile: "Faylni oldindan ko'rish",
    downloadFile: 'Faylni yuklash'
  },
  Empty: {
    description: 'Maʼlumot topilmadi'
  },
  Icon: {
    icon: 'ikonka'
  },
  Text: {
    edit: 'Tahrirlash',
    copy: 'Nusxalash',
    copied: 'Nusxalandi',
    expand: 'Ochib qoyish'
  },
  Form: {
    optional: '(shart emas)',
    defaultValidateMessages: {
      default: '${label} maydonini tekshirishda xatolik yuz berdi',
      required: 'Iltimos, ${label} kiriting',
      enum: '${label}, [${enum}] dan biri boʻlishi kerak',
      whitespace: '${label} boʻsh boʻlishi mumkin emas',
      date: {
        format: '${label} toʻgʻri sana formatida emas',
        parse: '${label} sanaga aylantirilmaydi',
        invalid: "${label} tog'ri sana emas"
      },
      types: {
        string: typeTemplate,
        method: typeTemplate,
        array: typeTemplate,
        object: typeTemplate,
        number: typeTemplate,
        date: typeTemplate,
        boolean: typeTemplate,
        integer: typeTemplate,
        float: typeTemplate,
        regexp: typeTemplate,
        email: typeTemplate,
        url: typeTemplate,
        hex: typeTemplate
      },
      string: {
        len: '${label}, ${len} ta belgidan iborat boʻlishi kerak',
        min: '${label} должна быть больше или равна ${min} символов',
        max: '${label}, ${max} belgidan katta yoki teng boʻlishi kerak',
        range: '${label} uzunligi ${min}-${max} belgilar orasida boʻlishi kerak'
      },
      number: {
        len: '${label}, ${len} ga teng boʻlishi kerak',
        min: '${label}, ${min} dan katta yoki teng boʻlishi kerak',
        max: '${label}, ${max} dan kichik yoki teng boʻlishi kerak',
        range: '${label}, ${min}-${max} orasida boʻlishi kerak'
      },
      array: {
        len: '${label} elementlari soni ${len} ga teng boʻlishi kerak',
        min: '${label} elementlari soni ${min} dan katta yoki teng boʻlishi kerak',
        max: '${label} elementlari soni ${max} dan kam yoki teng boʻlishi kerak',
        range: '${label} elementlari soni ${min} va ${max} orasida boʻlishi kerak'
      },
      pattern: {
        mismatch: '${label}, ${pattern} andazasiga mos emas'
      }
    }
  },
  Image: {
    preview: 'Ko‘rib chiqish'
  },
  QRCode: {
    expired: 'QR-kod eskirgan',
    refresh: 'Yangilash'
  }
};
var _default = exports.default = localeValues;