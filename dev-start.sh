#!/bin/bash

# 开发环境启动脚本

echo "🚀 启动开发环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动后端服务
echo "🔧 启动后端服务..."
npm run dev &
BACKEND_PID=$!

cd ..

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动前端服务
echo "🎨 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

cd ..

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📋 服务信息："
echo "  - 前端开发服务: http://localhost:5173"
echo "  - 后端开发服务: http://localhost:5000"
echo ""
echo "🛑 停止服务："
echo "  - 按 Ctrl+C 停止所有服务"
echo ""

# 等待用户中断
trap "echo '🛑 停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT

wait
