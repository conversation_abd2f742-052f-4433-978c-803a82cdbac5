import React, { useState } from 'react';
import {
  Card,
  Upload,
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Divider,
  Tag,
  Alert
} from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  FileTextOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { projectApi } from '../services/api';
import type { Project } from '../types';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SequenceUploadProps {
  project: Project;
  onSequenceAdded: () => void;
  onNext: () => void;
}

const SequenceUpload: React.FC<SequenceUploadProps> = ({ 
  project, 
  onSequenceAdded, 
  onNext 
}) => {
  const [uploading, setUploading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.fasta,.fa,.txt,.seq',
    beforeUpload: (file) => {
      const isValidType = ['.fasta', '.fa', '.txt', '.seq'].some(ext => 
        file.name.toLowerCase().endsWith(ext)
      );
      if (!isValidType) {
        message.error('只支持 FASTA (.fasta, .fa), TXT (.txt), SEQ (.seq) 格式的文件');
        return false;
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB');
        return false;
      }
      
      handleFileUpload(file);
      return false; // 阻止自动上传
    },
  };

  const handleFileUpload = async (file: File) => {
    setUploading(true);
    try {
      await projectApi.uploadSequences(project.id, file);
      message.success(`文件 ${file.name} 上传成功`);
      onSequenceAdded();
    } catch (error) {
      message.error('文件上传失败');
      console.error('Upload error:', error);
    } finally {
      setUploading(false);
    }
  };

  const handleAddSequence = async (values: { name: string; sequence: string }) => {
    try {
      await projectApi.addSequence(project.id, values);
      message.success('序列添加成功');
      setModalVisible(false);
      form.resetFields();
      onSequenceAdded();
    } catch (error) {
      message.error('添加序列失败');
      console.error('Add sequence error:', error);
    }
  };

  const columns = [
    {
      title: '序列名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '序列长度',
      dataIndex: 'sequence',
      key: 'length',
      render: (sequence: string) => (
        <Tag color="blue">{sequence.length} bp</Tag>
      ),
    },
    {
      title: '序列预览',
      dataIndex: 'sequence',
      key: 'preview',
      render: (sequence: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {sequence.substring(0, 50)}
          {sequence.length > 50 ? '...' : ''}
        </Text>
      ),
    },
    {
      title: '添加时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
  ];

  const sequences = project.sequences || [];
  const hasSequences = sequences.length > 0;

  return (
    <div>
      <Title level={4}>序列管理</Title>
      <Text type="secondary">
        上传FASTA格式的序列文件或手动添加序列，用于引物设计。
      </Text>

      <Divider />

      <div style={{ marginBottom: 24 }}>
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '24px',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card size="small" style={{
            width: '100%',
            maxWidth: 300,
            minWidth: 250
          }}>
            <Upload.Dragger {...uploadProps} style={{ padding: '20px 0' }}>
              <p className="ant-upload-drag-icon">
                <UploadOutlined style={{ fontSize: 48, color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持 .fasta, .fa, .txt, .seq 格式
              </p>
            </Upload.Dragger>
            {uploading && <div style={{ textAlign: 'center', marginTop: 8 }}>上传中...</div>}
          </Card>

          <div style={{ textAlign: 'center' }}>
            <Text type="secondary">或</Text>
          </div>

          <div>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
              size="large"
            >
              手动添加序列
            </Button>
          </div>
        </div>
      </div>

      {hasSequences && (
        <>
          <Alert
            message={`已添加 ${sequences.length} 条序列`}
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button 
                type="primary" 
                icon={<ArrowRightOutlined />}
                onClick={onNext}
              >
                下一步：参数设置
              </Button>
            }
          />

          <Card title="序列列表">
            <Table
              columns={columns}
              dataSource={sequences}
              rowKey="id"
              pagination={{
                pageSize: 5,
                showSizeChanger: false,
                showQuickJumper: false,
              }}
            />
          </Card>
        </>
      )}

      <Modal
        title="手动添加序列"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="添加"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddSequence}
        >
          <Form.Item
            name="name"
            label="序列名称"
            rules={[
              { required: true, message: '请输入序列名称' },
              { max: 100, message: '序列名称不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入序列名称" />
          </Form.Item>
          
          <Form.Item
            name="sequence"
            label="DNA序列"
            rules={[
              { required: true, message: '请输入DNA序列' },
              { 
                pattern: /^[ATCGatcg\s\n\r]+$/, 
                message: '序列只能包含A、T、C、G字符' 
              },
              { min: 50, message: '序列长度至少50个碱基' }
            ]}
          >
            <TextArea 
              rows={8}
              placeholder="请输入DNA序列（只包含A、T、C、G字符）"
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SequenceUpload;
