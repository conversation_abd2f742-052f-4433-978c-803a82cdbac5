/* Primer3 Web Interface 样式 */

/* 全局重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.ant-layout {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.sequence-preview {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
}

.primer-sequence {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  background: #f0f8ff;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  word-break: break-all;
}

.status-tag {
  font-weight: 500;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.parameter-section {
  margin-bottom: 24px;
}

.result-card {
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
}

.primer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.copy-button {
  opacity: 0.6;
  transition: opacity 0.3s;
}

.copy-button:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px !important;
  }

  .ant-layout-content {
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .ant-layout-header {
    padding: 0 12px !important;
  }

  .ant-layout-content {
    padding: 12px !important;
  }

  .ant-card-body {
    padding: 16px !important;
  }

  .ant-table-pagination {
    text-align: center;
  }

  .ant-pagination-options {
    display: none;
  }
}
