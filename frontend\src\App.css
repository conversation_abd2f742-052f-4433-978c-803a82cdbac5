/* Primer3 Web Interface 样式 */

.ant-layout {
  min-height: 100vh;
}

.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.sequence-preview {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
}

.primer-sequence {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  background: #f0f8ff;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  word-break: break-all;
}

.status-tag {
  font-weight: 500;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.parameter-section {
  margin-bottom: 24px;
}

.result-card {
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
}

.primer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.copy-button {
  opacity: 0.6;
  transition: opacity 0.3s;
}

.copy-button:hover {
  opacity: 1;
}
