const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testAPI() {
  try {
    console.log('🔍 测试后端API功能...\n');

    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ 健康检查:', healthResponse.data);

    // 2. 创建测试项目
    console.log('\n2. 创建测试项目...');
    const projectData = {
      name: '测试项目',
      description: '用于测试引物设计功能'
    };
    const projectResponse = await axios.post(`${API_BASE}/projects`, projectData);
    console.log('✅ 项目创建成功:', projectResponse.data);
    const projectId = projectResponse.data.id;

    // 3. 添加测试序列
    console.log('\n3. 添加测试序列...');
    const sequenceData = {
      name: '测试序列1',
      sequence: 'ATTTAAGATAGTGAGTATGGGGTTGGTTCAAATGGTAAGTGGAATGATGATTGCTCTTACTTTCTTTCCCCATTTGGGCTTAGGAAAGTCATGAAGATCCGAAGATGTATAGAGATGACCATGCAGTATGAACAACCACCCAGCCTCTGTGATAATACATGAGCTGGGAGTCAGAAACGGTAGTAGCTTGCATGTGTGGGGTGTGCAATTCTGAAGGCCATTAATTTTAACCTAGCTTGTTGTGTCAGTTTGGTGTGATACTTTGTAATAGTAATTAGACAAATAGTCGCAAAGGTTTGATCTTGCCATAGATAAGTAGGTGAGTGGTTATAGTTGATATTAGCCTTCCAGGCAGCACATTAGTCTTGCAGGATACCCTGTTCATCAACTCTGAAACTATTGTTTTAGTGTTCACTATGCCTG'
    };
    const sequenceResponse = await axios.post(`${API_BASE}/projects/${projectId}/sequences`, sequenceData);
    console.log('✅ 序列添加成功:', sequenceResponse.data);

    // 4. 获取项目详情
    console.log('\n4. 获取项目详情...');
    const projectDetailResponse = await axios.get(`${API_BASE}/projects/${projectId}`);
    console.log('✅ 项目详情:', {
      name: projectDetailResponse.data.name,
      sequences: projectDetailResponse.data.sequences?.length || 0,
      jobs: projectDetailResponse.data.jobs?.length || 0
    });

    // 5. 创建引物设计任务
    console.log('\n5. 创建引物设计任务...');
    const designData = {
      sequenceIds: [sequenceResponse.data.id],
      parameters: {
        PRIMER_OPT_SIZE: 20,
        PRIMER_MIN_SIZE: 18,
        PRIMER_MAX_SIZE: 25,
        PRIMER_OPT_TM: 60.0,
        PRIMER_MIN_TM: 57.0,
        PRIMER_MAX_TM: 63.0,
        PRIMER_MIN_GC: 20.0,
        PRIMER_MAX_GC: 80.0,
        PRIMER_MAX_POLY_X: 5,
        PRIMER_PRODUCT_SIZE_RANGE: '75-100 100-300 300-500',
        PRIMER_NUM_RETURN: 3
      }
    };
    const designResponse = await axios.post(`${API_BASE}/projects/${projectId}/design`, designData);
    console.log('✅ 引物设计任务创建成功:', designResponse.data);

    // 6. 等待一段时间后检查任务状态
    console.log('\n6. 等待引物设计完成...');
    const jobId = designResponse.data.jobIds[0];
    
    for (let i = 0; i < 10; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
      
      try {
        const jobResponse = await axios.get(`${API_BASE}/jobs/${jobId}`);
        console.log(`   检查任务状态 (${i + 1}/10): ${jobResponse.data.status}`);
        
        if (jobResponse.data.status === 'completed') {
          console.log('✅ 引物设计完成!');
          console.log('   引物数量:', jobResponse.data.result?.primers?.length || 0);
          if (jobResponse.data.result?.primers?.length > 0) {
            const primer = jobResponse.data.result.primers[0];
            console.log('   第一对引物:');
            console.log('     正向:', primer.left?.sequence || 'N/A');
            console.log('     反向:', primer.right?.sequence || 'N/A');
            console.log('     产物大小:', primer.product_size || 'N/A');
          }
          break;
        } else if (jobResponse.data.status === 'failed') {
          console.log('❌ 引物设计失败:', jobResponse.data.result);
          break;
        }
      } catch (error) {
        console.log(`   检查任务状态时出错: ${error.message}`);
      }
    }

    console.log('\n🎉 API测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testAPI();
