// 项目相关类型定义

export interface Project {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  sequences?: Sequence[];
  jobs?: PrimerJob[];
}

export interface Sequence {
  id: string;
  project_id: string;
  name: string;
  sequence: string;
  created_at: string;
}

export interface PrimerJob {
  id: string;
  project_id: string;
  sequence_id: string;
  parameters: PrimerParameters;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: PrimerResult;
  created_at: string;
  completed_at?: string;
}

export interface PrimerParameters {
  PRIMER_OPT_SIZE?: number;
  PRIMER_MIN_SIZE?: number;
  PRIMER_MAX_SIZE?: number;
  PRIMER_OPT_TM?: number;
  PRIMER_MIN_TM?: number;
  PRIMER_MAX_TM?: number;
  PRIMER_MIN_GC?: number;
  PRIMER_MAX_GC?: number;
  PRIMER_MAX_POLY_X?: number;
  PRIMER_PRODUCT_SIZE_RANGE?: string;
  PRIMER_NUM_RETURN?: number;
}

export interface PrimerResult {
  primers: Array<{
    index: number;
    left?: {
      sequence?: string;
      start?: string;
      len?: string;
      tm?: string;
      gc_percent?: string;
    };
    right?: {
      sequence?: string;
      start?: string;
      len?: string;
      tm?: string;
      gc_percent?: string;
    };
    product_size?: string;
  }>;
  statistics: Record<string, string>;
}
