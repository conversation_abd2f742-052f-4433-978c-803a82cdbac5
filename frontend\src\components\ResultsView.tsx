import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Alert,
  Modal,
  Descriptions,
  Progress,
  message,
  Tooltip,
  Input
} from 'antd';
import {
  ReloadOutlined,
  EyeOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { projectApi } from '../services/api';
import type { Project, PrimerJob } from '../types';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface ResultsViewProps {
  project: Project;
  onRefresh: () => void;
}

const ResultsView: React.FC<ResultsViewProps> = ({ project, onRefresh }) => {
  const [jobs, setJobs] = useState<PrimerJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState<PrimerJob | null>(null);

  useEffect(() => {
    setJobs(project.jobs || []);
  }, [project]);

  const handleRefresh = () => {
    setLoading(true);
    onRefresh();
    setTimeout(() => setLoading(false), 1000);
  };

  const handleViewDetail = async (jobId: string) => {
    try {
      const job = await projectApi.getJob(jobId);
      setSelectedJob(job);
      setDetailModalVisible(true);
    } catch (error) {
      message.error('获取任务详情失败');
      console.error('Get job detail error:', error);
    }
  };

  const handleCopySequence = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('序列已复制到剪贴板');
    });
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
      running: { color: 'processing', icon: <LoadingOutlined />, text: '运行中' },
      completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
      failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  const getSequenceName = (sequenceId: string) => {
    const sequence = project.sequences?.find(seq => seq.id === sequenceId);
    return sequence?.name || '未知序列';
  };

  const getProgressStats = () => {
    const total = jobs.length;
    const completed = jobs.filter(job => job.status === 'completed').length;
    const running = jobs.filter(job => job.status === 'running').length;
    const failed = jobs.filter(job => job.status === 'failed').length;
    const pending = jobs.filter(job => job.status === 'pending').length;

    return { total, completed, running, failed, pending };
  };

  const columns = [
    {
      title: '序列名称',
      dataIndex: 'sequence_id',
      key: 'sequence_name',
      render: (sequenceId: string) => (
        <Text strong>{getSequenceName(sequenceId)}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '引物数量',
      key: 'primer_count',
      render: (_: any, record: PrimerJob) => {
        if (record.status === 'completed' && record.result) {
          return <Tag color="blue">{record.result.primers.length} 对</Tag>;
        }
        return <Text type="secondary">-</Text>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (text: string) => text ? new Date(text).toLocaleString('zh-CN') : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PrimerJob) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record.id)}
            disabled={record.status !== 'completed'}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const stats = getProgressStats();
  const progressPercent = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

  if (jobs.length === 0) {
    return (
      <Alert
        message="暂无引物设计任务"
        description="请先在参数设置标签页中创建引物设计任务。"
        type="info"
        showIcon
      />
    );
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: 24 
      }}>
        <Title level={4}>引物设计结果</Title>
        <Button 
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
        >
          刷新状态
        </Button>
      </div>

      {/* 进度统计 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <Text strong>任务进度: </Text>
          <Progress 
            percent={progressPercent} 
            status={stats.failed > 0 ? 'exception' : 'active'}
            style={{ width: 200, display: 'inline-block', marginLeft: 8 }}
          />
          <Text style={{ marginLeft: 16 }}>
            {stats.completed}/{stats.total} 已完成
          </Text>
        </div>
        
        <Space size="large">
          <Space>
            <Tag color="success" icon={<CheckCircleOutlined />}>
              已完成: {stats.completed}
            </Tag>
          </Space>
          <Space>
            <Tag color="processing" icon={<LoadingOutlined />}>
              运行中: {stats.running}
            </Tag>
          </Space>
          <Space>
            <Tag color="default" icon={<ClockCircleOutlined />}>
              等待中: {stats.pending}
            </Tag>
          </Space>
          {stats.failed > 0 && (
            <Space>
              <Tag color="error" icon={<ExclamationCircleOutlined />}>
                失败: {stats.failed}
              </Tag>
            </Space>
          )}
        </Space>
      </Card>

      {/* 任务列表 */}
      <Card title="任务列表">
        <Table
          columns={columns}
          dataSource={jobs}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个任务`,
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="引物设计详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedJob(null);
        }}
        footer={null}
        width={1000}
      >
        {selectedJob && (
          <div>
            <Descriptions bordered size="small" style={{ marginBottom: 24 }}>
              <Descriptions.Item label="序列名称">
                {getSequenceName(selectedJob.sequence_id)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedJob.status)}
              </Descriptions.Item>
              <Descriptions.Item label="引物对数量">
                {selectedJob.result?.primers.length || 0}
              </Descriptions.Item>
            </Descriptions>

            {selectedJob.result && selectedJob.result.primers.length > 0 && (
              <Card title="引物序列" size="small">
                {selectedJob.result.primers.map((primer, index) => (
                  <Card key={index} size="small" style={{ marginBottom: 16 }}>
                    <Title level={5}>引物对 {index + 1}</Title>
                    
                    {primer.left && (
                      <div style={{ marginBottom: 12 }}>
                        <Text strong>正向引物: </Text>
                        <div style={{ 
                          background: '#f5f5f5', 
                          padding: '8px 12px', 
                          borderRadius: 4,
                          fontFamily: 'monospace',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <Text code>{primer.left.sequence}</Text>
                          <Tooltip title="复制序列">
                            <Button 
                              type="text" 
                              size="small"
                              icon={<CopyOutlined />}
                              onClick={() => handleCopySequence(primer.left?.sequence || '')}
                            />
                          </Tooltip>
                        </div>
                        <Space style={{ marginTop: 4 }}>
                          <Text type="secondary">Tm: {primer.left.tm}°C</Text>
                          <Text type="secondary">GC: {primer.left.gc_percent}%</Text>
                          <Text type="secondary">长度: {primer.left.len}bp</Text>
                        </Space>
                      </div>
                    )}

                    {primer.right && (
                      <div style={{ marginBottom: 12 }}>
                        <Text strong>反向引物: </Text>
                        <div style={{ 
                          background: '#f5f5f5', 
                          padding: '8px 12px', 
                          borderRadius: 4,
                          fontFamily: 'monospace',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <Text code>{primer.right.sequence}</Text>
                          <Tooltip title="复制序列">
                            <Button 
                              type="text" 
                              size="small"
                              icon={<CopyOutlined />}
                              onClick={() => handleCopySequence(primer.right?.sequence || '')}
                            />
                          </Tooltip>
                        </div>
                        <Space style={{ marginTop: 4 }}>
                          <Text type="secondary">Tm: {primer.right.tm}°C</Text>
                          <Text type="secondary">GC: {primer.right.gc_percent}%</Text>
                          <Text type="secondary">长度: {primer.right.len}bp</Text>
                        </Space>
                      </div>
                    )}

                    {primer.product_size && (
                      <div>
                        <Text strong>产物大小: </Text>
                        <Tag color="green">{primer.product_size} bp</Tag>
                      </div>
                    )}
                  </Card>
                ))}
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ResultsView;
